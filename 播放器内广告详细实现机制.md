# 播放器内广告详细实现机制

## 概述

播放器内广告是通过 iframe 跨窗口通信机制实现的，父页面（播放器页面）通过 `WindowMessenger` 类向播放器 iframe 传递广告数据，播放器内部根据播放状态在适当时机展示广告。

## 播放器内广告类型

### 1. 播放前广告 (BeforePlay)
- **展示时机**: 视频开始播放前
- **用途**: 前贴片广告，用户点击播放按钮后首先看到的广告
- **特点**: 通常有倒计时，可跳过或强制观看

### 2. 播放中广告 (DuringPlay)
- **展示时机**: 视频播放过程中
- **用途**: 中插广告，在视频播放到特定时间点时插入
- **特点**: 可以是暂停播放显示广告，或叠加在视频上方

### 3. 播放暂停广告 (PausePlay)
- **展示时机**: 用户暂停视频时
- **用途**: 暂停时的覆盖广告
- **特点**: 用户恢复播放时消失

## 技术实现架构

### 1. 父页面实现 (pages/player/[id].vue)

#### 广告数据获取
```typescript
// 获取播放器内广告数据
const playerInnerAds = getAdvertisementsByPositions([
    AdvertisementPosition.DuringPlay,    // 播放中广告
    AdvertisementPosition.PausePlay,     // 播放暂停广告
    AdvertisementPosition.BeforePlay     // 播放前广告
])
```

#### WindowMessenger 初始化
```typescript
const windowMessenger = new WindowMessenger({
    targetOrigin: '*',
    onMessage: (data, event) => {
        console.log('Received message:', data, event)
    }
})
```

#### 广告数据传递流程
```typescript
onMounted(async () => {
    // 1. 绑定消息监听
    windowMessenger.Bind(window)
    
    // 2. 注册初始化处理器
    windowMessenger.onInit(() => {
        if (videoDetail.value) {
            // 3. 发送初始化数据到播放器
            windowMessenger.sendInit({
                videoPic: videoDetail.value.videoPic,
                ads: JSON.parse(JSON.stringify(getAdvertisementsByPositions([
                    AdvertisementPosition.DuringPlay, 
                    AdvertisementPosition.PausePlay, 
                    AdvertisementPosition.BeforePlay
                ])))
            })
        }
    })
    
    // 4. 设置目标窗口为播放器iframe
    nextTick(() => {
        windowMessenger.setWindow(iframePlayerRef.value?.contentWindow)
        // 5. 加载播放器页面
        handleChangePlayerUrl(currentPlayerItem.value)
    })
})
```

### 2. 播放器 iframe 实现（推测）

虽然代码中没有显示播放器内部的具体实现，但根据通信机制可以推测播放器内部的实现：

#### 消息接收处理
```javascript
// 播放器内部代码（推测）
class PlayerAdManager {
    constructor() {
        this.ads = []
        this.initMessageListener()
    }
    
    initMessageListener() {
        window.addEventListener('message', (event) => {
            const { type, data } = event.data
            
            if (type === 'onInit') {
                this.handleInit(data)
            }
        })
    }
    
    handleInit(data) {
        this.videoPic = data.videoPic
        this.ads = data.ads
        this.initAds()
    }
    
    initAds() {
        // 根据广告位置分类处理
        this.beforePlayAds = this.ads.filter(ad => ad.position === 5) // BeforePlay
        this.duringPlayAds = this.ads.filter(ad => ad.position === 6) // DuringPlay
        this.pausePlayAds = this.ads.filter(ad => ad.position === 7)  // PausePlay
    }
}
```

#### 广告展示逻辑
```javascript
// 播放器内部广告展示逻辑（推测）
class VideoPlayer {
    constructor() {
        this.adManager = new PlayerAdManager()
        this.video = document.querySelector('video')
        this.initEventListeners()
    }
    
    initEventListeners() {
        // 播放前广告
        this.video.addEventListener('play', () => {
            if (this.shouldShowBeforePlayAd()) {
                this.showBeforePlayAd()
            }
        })
        
        // 播放中广告
        this.video.addEventListener('timeupdate', () => {
            this.checkDuringPlayAds()
        })
        
        // 暂停广告
        this.video.addEventListener('pause', () => {
            this.showPausePlayAd()
        })
        
        this.video.addEventListener('play', () => {
            this.hidePausePlayAd()
        })
    }
    
    showBeforePlayAd() {
        const ad = this.adManager.beforePlayAds[0]
        if (ad) {
            this.createAdOverlay(ad)
            this.video.pause() // 暂停视频播放
        }
    }
    
    checkDuringPlayAds() {
        const currentTime = this.video.currentTime
        // 检查是否到达广告插入时间点
        const ad = this.adManager.duringPlayAds.find(ad => 
            this.shouldShowAtTime(ad, currentTime)
        )
        if (ad) {
            this.showDuringPlayAd(ad)
        }
    }
    
    showPausePlayAd() {
        const ad = this.adManager.pausePlayAds[0]
        if (ad) {
            this.createAdOverlay(ad)
        }
    }
    
    createAdOverlay(ad) {
        const overlay = document.createElement('div')
        overlay.className = 'ad-overlay'
        overlay.innerHTML = `
            <div class="ad-content">
                <img src="${ad.imageUrl}" alt="广告">
                <div class="ad-close" onclick="this.closeAd()">×</div>
            </div>
        `
        document.body.appendChild(overlay)
    }
}
```

## 广告数据结构

### 传递给播放器的数据格式
```typescript
interface PlayerInitData {
    videoPic: string;           // 视频封面图
    ads: AdvertisementModel[];  // 广告数组
}

interface AdvertisementModel {
    id: string;              // 广告ID
    position: number;        // 广告位置 (5=播放前, 6=播放中, 7=暂停)
    parameters?: string;     // 广告参数（可能包含展示时机等配置）
    imageUrl?: string;       // 广告图片URL
    redirectUrl?: string;    // 点击跳转URL
    sort: number;           // 排序权重
}
```

### 广告参数配置（推测）
```json
{
    "parameters": {
        "duration": 15,           // 广告展示时长（秒）
        "skippable": true,        // 是否可跳过
        "skipDelay": 5,          // 跳过按钮出现延迟（秒）
        "insertTime": [30, 60],  // 播放中广告插入时间点（秒）
        "autoClose": false       // 是否自动关闭
    }
}
```

## 消息通信协议

### 1. 父页面 → 播放器
```typescript
// 初始化消息
{
    type: 'onInit',
    data: {
        videoPic: string,
        ads: AdvertisementModel[]
    }
}
```

### 2. 播放器 → 父页面
```typescript
// 视频结束消息
{
    type: 'onVideoEnded',
    data: {
        manifestUri: string  // 当前播放的视频URL
    }
}

// 广告事件消息（推测）
{
    type: 'onAdEvent',
    data: {
        adId: string,
        event: 'show' | 'click' | 'close' | 'skip',
        timestamp: number
    }
}
```

## 广告展示时机控制

### 1. 播放前广告
```javascript
// 在视频开始播放时触发
video.addEventListener('loadstart', () => {
    if (hasBeforePlayAd()) {
        showBeforePlayAd()
        video.pause()
    }
})
```

### 2. 播放中广告
```javascript
// 监听播放进度
video.addEventListener('timeupdate', () => {
    const currentTime = video.currentTime
    const ad = findDuringPlayAdAtTime(currentTime)
    if (ad && !ad.shown) {
        showDuringPlayAd(ad)
        ad.shown = true
    }
})
```

### 3. 播放暂停广告
```javascript
// 监听暂停事件
video.addEventListener('pause', () => {
    if (!video.ended) {
        showPausePlayAd()
    }
})

video.addEventListener('play', () => {
    hidePausePlayAd()
})
```

## 广告样式和交互

### CSS 样式（推测）
```css
.ad-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ad-content {
    position: relative;
    max-width: 80%;
    max-height: 80%;
}

.ad-content img {
    width: 100%;
    height: auto;
    cursor: pointer;
}

.ad-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
}

.ad-skip {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
}
```

## 使用流程总结

### 1. 开发者配置广告
```typescript
// 在后台管理系统中配置广告
const ad = {
    position: AdvertisementPosition.BeforePlay,
    imageUrl: '/images/ad-banner.jpg',
    redirectUrl: 'https://example.com/product',
    parameters: JSON.stringify({
        duration: 15,
        skippable: true,
        skipDelay: 5
    }),
    sort: 1
}
```

### 2. 系统自动获取和传递
- 应用启动时获取所有广告数据
- 播放器页面加载时筛选播放器内广告
- 通过 WindowMessenger 传递给播放器 iframe

### 3. 播放器自动展示
- 播放器接收广告数据并初始化
- 根据播放状态在适当时机展示广告
- 处理用户交互（点击、跳过、关闭）

### 4. 事件反馈（推测）
- 播放器向父页面反馈广告事件
- 用于统计广告展示效果和用户行为

## 优势特点

1. **解耦设计**: 广告逻辑与播放器核心功能分离
2. **灵活配置**: 通过参数配置控制广告行为
3. **跨域安全**: 使用 PostMessage 实现安全的跨窗口通信
4. **统一管理**: 所有广告数据统一获取和分发
5. **扩展性强**: 易于添加新的广告位置和类型

## 注意事项

1. **性能影响**: 广告展示可能影响视频播放性能
2. **用户体验**: 需要平衡广告收益和用户体验
3. **兼容性**: 确保在不同浏览器和设备上正常工作
4. **安全性**: 防止恶意广告内容和XSS攻击
5. **统计准确**: 确保广告展示和点击统计的准确性
