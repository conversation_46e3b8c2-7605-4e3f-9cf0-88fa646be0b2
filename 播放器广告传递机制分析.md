# 播放器广告传递机制分析

## 概述

在 `pages/player/[id].vue` 页面中，播放器广告通过多种方式进行传递和展示，包括页面级广告和播放器内广告。本文档详细分析了广告数据的获取、传递和展示机制。

## 广告位置枚举

系统定义了多种广告位置，播放器相关的广告位置包括：

```typescript
export enum AdvertisementPosition {
  PlayerTopRight = 2,    // 播放页顶部右侧
  PlayerBottom = 3,      // 播放器底部
  BeforePlay = 5,        // 播放前
  DuringPlay = 6,        // 播放中
  PausePlay = 7,         // 播放暂停
}
```

## 广告数据模型

```typescript
export interface AdvertisementModel {
  id: string;              // 广告ID
  position: AdvertisementPosition;  // 广告位置
  parameters?: string;     // 广告参数
  imageUrl?: string;       // 图片URL
  redirectUrl?: string;    // 跳转URL
  sort: number;           // 排序
}
```

## 广告传递机制

### 1. 全局广告数据获取

在 `app.vue` 中，应用启动时获取所有广告数据：

```typescript
const advertisement = useAdvertisement()
onMounted(async() => {
  advertisement.value = await useFetchApi<AdvertisementModel[]>('/api/v1/advertisements')
})
```

### 2. 页面级广告获取

在播放器页面中，通过工具函数获取特定位置的广告：

```typescript
// 获取播放页顶部右侧广告
const playerTopRightAd = getFirstAdvertisementByPosition(AdvertisementPosition.PlayerTopRight)

// 获取播放器底部广告
const playerBottomAd = getFirstAdvertisementByPosition(AdvertisementPosition.PlayerBottom)
```

### 3. 页面级广告展示

#### 播放页顶部右侧广告
```vue
<div v-show="playerTopRightAd" class="viewpic1">
    <img :src="playerTopRightAd?.imageUrl" alt="">
</div>
```

#### 播放器底部广告
```vue
<div v-show="playerBottomAd" class="viewpic2">
    <img :src="playerBottomAd?.imageUrl" alt="">
</div>
```

### 4. 播放器内广告传递

播放器内广告通过 `WindowMessenger` 类进行跨窗口通信传递：

#### WindowMessenger 初始化
```typescript
const windowMessenger = new WindowMessenger({
    targetOrigin: '*',
    onMessage: (data, event) => {
        console.log('Received message:', data, event)
    }
})
```

#### 广告数据传递
在组件挂载时，通过消息机制向播放器 iframe 传递广告数据：

```typescript
onMounted(async () => {
    windowMessenger.Bind(window)
    windowMessenger.onInit(() => {
        if (videoDetail.value) {
            windowMessenger.sendInit({
                videoPic: videoDetail.value.videoPic,
                ads: JSON.parse(JSON.stringify(getAdvertisementsByPositions([
                    AdvertisementPosition.DuringPlay, 
                    AdvertisementPosition.PausePlay, 
                    AdvertisementPosition.BeforePlay
                ])))
            })
        }
    })
    
    // 设置目标窗口为播放器 iframe
    windowMessenger.setWindow(iframePlayerRef.value?.contentWindow)
})
```

## 广告工具函数

### 获取广告数据的工具函数

```typescript
// 获取所有广告
export const getAdvertisements = (): AdvertisementModel[] => {
    return useAdvertisement().value
}

// 根据单个位置获取广告
export const getAdvertisementsByPosition = (position: AdvertisementPosition): AdvertisementModel[] => {
    return getAdvertisements()
        .filter((ad) => ad.position === position)
        .sort((a, b) => a.sort - b.sort)
}

// 根据多个位置获取广告
export const getAdvertisementsByPositions = (positions: AdvertisementPosition[]): AdvertisementModel[] => {
    return getAdvertisements()
        .filter((ad) => positions.includes(ad.position))
        .sort((a, b) => a.sort - b.sort)
}

// 获取指定位置的第一个广告
export const getFirstAdvertisementByPosition = (
    position: AdvertisementPosition,
): AdvertisementModel | null => {
    const ads = getAdvertisementsByPosition(position)
    return ads.length > 0 ? ads[0] : null
}
```

## 消息通信机制

### WindowMessenger 类功能

1. **消息绑定**: 绑定到指定窗口监听消息
2. **消息发送**: 向目标窗口发送消息
3. **事件处理**: 处理特定类型的消息事件
4. **生命周期管理**: 提供销毁方法清理资源

### 关键方法

- `sendInit(data)`: 发送初始化数据到播放器
- `onInit(handler)`: 注册初始化消息处理器
- `onVideoEnded(handler)`: 注册视频结束事件处理器
- `setWindow(window)`: 设置目标通信窗口

## 广告传递流程

1. **应用启动**: 在 `app.vue` 中获取所有广告数据并存储到全局状态
2. **页面加载**: 播放器页面根据位置枚举获取对应的广告数据
3. **页面展示**: 直接在模板中展示页面级广告（顶部右侧、底部）
4. **播放器通信**: 通过 WindowMessenger 向播放器 iframe 传递播放器内广告数据
5. **播放器接收**: 播放器 iframe 接收广告数据并在适当时机展示

## 广告类型分类

### 页面级广告
- **播放页顶部右侧广告**: 显示在播放器右侧边栏顶部
- **播放器底部广告**: 显示在播放器下方

### 播放器内广告
- **播放前广告**: 视频开始播放前展示
- **播放中广告**: 视频播放过程中展示
- **播放暂停广告**: 视频暂停时展示

## 技术特点

1. **统一管理**: 所有广告数据通过统一的 API 获取和管理
2. **位置灵活**: 通过枚举定义广告位置，便于扩展和维护
3. **跨窗口通信**: 使用 PostMessage API 实现父页面与 iframe 的通信
4. **响应式展示**: 基于 Vue 3 的响应式系统，广告数据变化时自动更新展示
5. **排序支持**: 广告支持排序字段，可控制同位置多个广告的展示顺序

## 总结

播放器广告传递机制采用了分层设计：
- **数据层**: 统一的广告数据获取和管理
- **传递层**: 基于 WindowMessenger 的跨窗口通信
- **展示层**: 页面级直接展示和播放器内动态展示

这种设计既保证了广告展示的灵活性，又维持了代码的可维护性和扩展性。
