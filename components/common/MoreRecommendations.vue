<template>
  <view class="box">
    <view class="title">更多推荐</view>
    <ul class="tuijianlist">
      <li v-for="(item, index) in recommendationList" :key="index">
        <view class="cover">
          <view @click="handleNavigateToVideo(item.id)">
            <image :src="item.videoPic || 'https://placehold.co/500x283'" mode="widthFix"/>
          </view>
        </view>
        <view class="con">
          <view @click="handleNavigateToVideo(item.id)">
            <view class="biaoti">{{ item.videoName }}</view>
          </view>
          <view class="bottom">
            <navigator :url="`/pages/list/list?videoActor=${encodeURIComponent(item.videoActor || '')}&title=${encodeURIComponent('主演：' + (item.videoActor || '未知'))}`" v-if="item.videoActor">
              <view class="zhuyan">主演：{{ item.videoActor || '未知' }}</view>
            </navigator>
            <view class="zhuyan" v-else>主演：未知</view>
            <view class="time">{{ formatDate(item.videoUpdateTime || item.videoAddTime) }}</view>
          </view>
        </view>
      </li>
    </ul>
  </view>
</template>

<script>
import logger from '@/utils/logger';

export default {
  name: 'MoreRecommendations',
  props: {
    recommendationList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 跳转到视频详情页
    handleNavigateToVideo(videoId) {
      if (!videoId) return;

      uni.navigateTo({
        url: `/pages/view/view?id=${videoId}`
      });
    },

    /**
     * 格式化日期
     * @param {string} dateStr - 日期字符串
     * @returns {string} 格式化后的日期字符串 (YYYY-MM-DD)
     */
    formatDate(dateStr) {
      if (!dateStr) return '未知';

      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '未知';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        logger.error('日期格式化错误:', error);
        return '未知';
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tuijianlist {
  display: flex;
  flex-direction: column;
  gap: 40rpx;

  li {
    display: flex;
    gap: 20rpx;
    width: 100%;

    .con {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .biaoti {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .zhuyan{
        margin-bottom: 20rpx;
      }

      .zhuyan, .time {
        font-size: 24rpx;
        color: #969699;
      }
    }

    .cover {
      min-width: 40vw;
      flex: none;
      height: auto;

      image {
        width: 100%;
        display: block;
      }

      .video-info {
        position: absolute;
        width: calc(100% - 20rpx);
        padding: 0 10rpx;
        bottom: 0;
        height: 48rpx;
        font-size: 20rpx;
        line-height: 48rpx;
        background: linear-gradient(0deg, #101012, rgba(16, 16, 18, 0));
        display: flex;
        flex-direction: column;
        justify-content: flex-end;

        .renqitime {
          display: flex;
          align-items: center;
          justify-content: space-between;

          span:first-child {
            display: flex;
            align-items: center;
          }

          span:first-child:before {
            content: "";
            background: url("static/renqi.png") left center no-repeat;
            width: 24rpx;
            height: 24rpx;
            background-size: 100%;
            display: block;
            margin-right: 6rpx;
          }
        }
      }
    }
  }
}
</style>
