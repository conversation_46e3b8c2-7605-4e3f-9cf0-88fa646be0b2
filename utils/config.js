/**
 * 统一配置模块
 * 提供应用的环境配置信息和配置加载功能
 */

// 环境配置
export const ENV = {
  DEV: {
    // 开发环境
    apiBaseUrl: 'https://api.player.4ii.cc',
    // apiBaseUrl: 'http://10.1.1.111:44367',
    timeout: 10000,
    appName: 'ST影视(开发)',
    appVersion: '1.0.0',
    buildNumber: '1'
  },
  TEST: {
    apiBaseUrl: 'http://test-api.stmovie.com',
    timeout: 10000,
    appName: 'ST影视(测试)',
    appVersion: '1.0.0',
    buildNumber: '1'
  },
  PROD: {
    apiBaseUrl: 'https://api.stmovie.com',
    timeout: 15000,
    appName: 'ST影视',
    appVersion: '1.0.0',
    buildNumber: '1'
  }
};

/**
 * 获取当前环境
 * @returns {string} 当前环境代码 ('DEV', 'TEST', 'PROD')
 */
export function getEnvironment() {
  switch (process.env.NODE_ENV) {
    case 'development':
      return 'DEV';
    case 'test':
      return 'TEST';
    case 'production':
    default:
      return 'PROD';
  }
}

// 当前环境
export const currentEnv = getEnvironment();

// 当前环境的配置
export const config = ENV[currentEnv];

/**
 * 获取配置
 * @returns {Object} 配置对象
 */
export function getConfig() {
  return config;
}

/**
 * 加载环境配置
 * 尝试从本地存储或远程服务器获取配置
 * @returns {Promise<Object>} 配置对象
 */
export async function loadConfig() {
  try {
    // 使用当前环境配置作为默认值
    const defaultConfig = {
      appName: config.appName,
      baseUrl: config.apiBaseUrl,
      timeout: config.timeout
    };

    // 尝试从本地存储获取配置
    const storageConfig = uni.getStorageSync('APP_CONFIG');
    if (storageConfig) {
      return { ...defaultConfig, ...JSON.parse(storageConfig) };
    }

    // 尝试从远程获取配置
    const response = await uni.request({
      url: defaultConfig.baseUrl + '/getEnvConfig',
      method: 'GET',
      timeout: 5000
    });

    if (response.statusCode === 200 && response.data) {
      const remoteConfig = response.data;
      // 保存到本地存储
      uni.setStorageSync('APP_CONFIG', JSON.stringify(remoteConfig));
      return { ...defaultConfig, ...remoteConfig };
    }

    return defaultConfig;
  } catch (error) {
    console.warn('Failed to load remote config, using default config:', error);
    return {
      appName: config.appName,
      baseUrl: config.apiBaseUrl,
      timeout: config.timeout
    };
  }
}

// 为了向后兼容，导出默认配置
export default config;
