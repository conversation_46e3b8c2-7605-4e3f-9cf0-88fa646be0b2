# 移动端播放器广告功能修复说明

## 修复内容

### 问题描述
移动端播放器无法正常显示播放器内广告（播放前广告、播放中广告、暂停广告），而PC端功能正常。

### 根本原因
1. **播放器URL设置方式不同**：移动端直接设置播放地址，而PC端使用iframe + 静态HTML文件
2. **广告数据传递时机不当**：在播放器未完全加载时就发送广告数据
3. **消息通信目标窗口设置错误**：在iframe加载前就设置目标窗口

### 修复方案

#### 1. 统一播放器URL设置方式
```javascript
// 与PC端保持完全一致：直接设置播放地址
const playUrl = this.videoDetail?.videoPlayServer + item.url;
iframe.src = playUrl;

// 立即设置消息通信目标窗口
if (this.messenger) {
  this.messenger.setWindow(iframe.contentWindow);
}
```

#### 2. 优化广告数据传递旹式
```javascript
// 完全参考PC端实现：在initMessenger的onInit回调中发送广告数据
this.messenger.onInit(() => {
  if (this.videoDetail) {
    const playerAds = getAdvertisementsByPositions(this.allAdvertisements, [
      AdvertisementPosition.DuringPlay,
      AdvertisementPosition.PausePlay,
      AdvertisementPosition.BeforePlay
    ]);

    this.messenger.sendInit({
      videoPic: this.videoDetail.videoPic,
      ads: JSON.parse(JSON.stringify(playerAds))
    });
  }
});
```

#### 3. 统一消息通信机制
- 在`handleChangePlayerUrl`中设置消息通信目标窗口
- 在`initMessenger`的`onInit`回调中发送广告数据
- 与PC端实现方式完全一致

## 技术特点

### 1. 与PC端保持一致
- 使用相同的广告位置枚举
- 使用相同的工具函数
- 使用相同的消息通信机制
- 使用相同的数据传递格式

### 2. 完整的广告支持
- **播放前广告**：视频开始播放前显示，支持倒计时
- **播放中广告**：视频播放30秒后显示，右上角小窗口
- **暂停广告**：视频暂停时显示，全屏覆盖

### 3. 用户交互功能
- 广告倒计时自动关闭
- 点击广告跳转到指定链接
- 手动关闭广告按钮
- 广告点击统计

## 文件修改清单

### 主要修改文件
1. **pages/view/view.vue**
   - 修改`handleChangePlayerUrl`方法
   - 优化`initMessenger`方法
   - 改进iframe加载事件处理

### 依赖文件（无需修改）
1. **static/player.html** - 播放器HTML文件（已包含完整广告逻辑）
2. **utils/windowMessenger.js** - 消息通信工具
3. **api/advertisement.js** - 广告API和工具函数

## 测试验证

### 测试步骤
1. 启动移动端应用
2. 进入任意视频播放页面
3. 观察播放器加载过程
4. 验证以下广告功能：
   - 播放前广告是否显示
   - 播放中广告是否在30秒后显示
   - 暂停时广告是否显示
   - 广告倒计时是否正常
   - 广告点击跳转是否正常

### 调试信息
修复后会输出详细的调试日志：
```
[播放器广告] 发送初始化消息到播放器
[播放器广告] 广告数据发送完成，广告数量: X个
[播放器] 收到父窗口消息: onInit {ads: [...]}
[播放器] 广告数据已更新
[播放器] 显示播放前广告
```

## 预期效果

修复完成后，移动端播放器广告功能将与PC端完全一致：
1. 播放前广告正常显示，支持10秒倒计时
2. 播放中广告在播放30秒后显示，支持5秒倒计时
3. 暂停广告在视频暂停时显示
4. 所有广告支持点击跳转和手动关闭
5. 广告数据从后端API正常获取和显示

## 注意事项

1. **静态文件路径**：确保`/static/player.html`文件可以正常访问
2. **跨域问题**：iframe通信需要正确的域名配置
3. **广告数据**：确保后端API返回正确的广告数据
4. **测试环境**：建议在真实设备上测试，模拟器可能有限制

## 后续优化建议

1. **性能优化**：考虑预加载播放器HTML文件
2. **错误处理**：增加广告加载失败的降级处理
3. **用户体验**：添加广告跳过功能
4. **数据统计**：增加广告展示和点击统计
