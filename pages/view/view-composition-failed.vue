<template>
    <div class="main">
        <div class="box view">
            <div class="vcontent">
                <div v-if="!videoDetail" class="no-results">
                    <span><img src="@/assets/img/nofind.png" alt="没有找到"></span>
                    <span>{{ errorMessages }}</span>
                    <span>换个试试吧！</span>
                </div>
                <div v-if="videoDetail" class="player">
                    <iframe
                        id="iframePlayer"
                        ref="iframePlayerRef"
                        src="about:blank"
                        frameborder="0"
                        style="width: 100%"
                        height="100%;"
                        border="0"
                        marginwidth="0"
                        marginheight="0"
                        scrolling="no"
                        allowfullscreen
                        mozallowfullscreen="mozallowfullscreen"
                        msallowfullscreen="msallowfullscreen"
                        oallowfullscreen="oallowfullscreen"
                        webkitallowfullscreen="webkitallowfullscreen" />
                </div>
                <div class="vdatabar">
                    <span><a href="javascript:void(0)"><i class="iconfont iconzan" />{{ videoDetail?.videoUp }}</a></span>
                    <span><a href="javascript:void(0)"><i class="iconfont iconcai" />{{ videoDetail?.videoDown }}</a></span>
                    <span><a href="javascript:void(0)"><i class="iconfont iconrenqi" />{{ videoDetail?.videoHits }}</a></span>
                </div>
                <div v-show="playerBottomAd" class="viewpic2">
                    <img :src="playerBottomAd?.imageUrl" alt="">
                </div>
                <div class="video-detail">
                    <div class="title">{{ videoDetail?.videoName }}</div>
                    <div class="linear">
                        <span class="toggle-intro" :class="{ 'on': isIntroOpen }" @click="handleToggleIntro">
                            <i class="iconfont iconxiangqing" />简介<i class="iconfont iconjiantou_liebiaozhankai" />
                        </span>
                        <span>
                            <a v-for="tag in videoDetail?.videoTag?.split(',')" :key="tag" href="javascript:void(0)">{{ tag }}</a>
                        </span>
                        <span><i class="iconfont iconrenqi" />{{ videoDetail?.videoHits }}</span>
                        <span>
                            <div class="line" />{{ videoDetail?.videoScore }}分
                        </span>
                    </div>
                    <div ref="introRef" class="intro" :class="{ 'active': isIntroOpen }"
                        :style="{ maxHeight: isIntroOpen ? introMaxHeight + 'px' : '0px' }">
                        <span><img :src="videoDetail?.videoPic" alt=""></span>
                        <ul>
                            <li>{{ videoDetail?.videoName }}</li>
                            <li><span v-for="tag in videoDetail?.videoTag?.split(',')" :key="tag" href="javascript:void(0)">{{ tag }}</span></li>
                            <li>{{ videoDetail?.videoBlurb }}</li>
                            <li>主演：{{ videoDetail?.videoActor }}</li>
                        </ul>
                    </div>

                    <div v-if="playerItemList.length > 1" class="playlist">
                        <ul>
                            <li v-for="item in playerItemList" :key="item.url"
                                :class="{ 'on': currentPlayerItem === item }">
                                <a href="javascript:void(0)" @click="handleChangePlayerUrl(item)">{{ item.label }}</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="content view2">
                    <h1>猜你喜欢</h1>
                    <ClientOnly>
                        <component
                            :is="isHorizontalLayout ? LikeVideoHorizontal : LikeVideoVertical"
                            :like-video-list="likeVideoList"
                            @click-player="handleClickPlayer" />
                    </ClientOnly>
                </div>
            </div>
            <div class="vsidebar">
                <div v-show="playerTopRightAd" class="viewpic1">
                    <img :src="playerTopRightAd?.imageUrl" alt="">
                </div>
                <div class="vsidebar-list">
                    <h1>即将播放</h1>
                    <ClientOnly>
                        <component
                            :is="isHorizontalLayout ? ComingSoonHorizontal : ComingSoonVertical"
                            :coming-soon-video-list="comingSoonVideoList"
                            @click-player="handleClickPlayer" />
                    </ClientOnly>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { AdvertisementPosition } from '~/server/models/enums/AdvertisementPosition'
import type { VideoModel, VodPlayItem } from '~/server/models/VideoModel'
import { WindowMessenger } from '@/utils/windowMessenger'
import { parseVodPlayUrl } from '@/utils/vodPlayer'
import logger from '@/utils/logger'

// 工具函数
const parseTemplate = (template: string, params: Record<string, string>) => {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => params[key] || match)
}

const GetNavigationExtraParamsVideoTypeName = (navigations: any[], videoType: string) => {
  // 简化实现，返回默认值
  return { isHorizontalLayout: false }
}

// 广告数据
const playerTopRightAd = getFirstAdvertisementByPosition(AdvertisementPosition.PlayerTopRight)
const playerBottomAd = getFirstAdvertisementByPosition(AdvertisementPosition.PlayerBottom)

// 异步组件
const ComingSoonVertical = defineAsyncComponent(() => import('~/components/ComingSoonVertical.vue'))
const ComingSoonHorizontal = defineAsyncComponent(() => import('~/components/ComingSoonHorizontal.vue'))
const LikeVideoVertical = defineAsyncComponent(() => import('~/components/LikeVideoVertical.vue'))
const LikeVideoHorizontal = defineAsyncComponent(() => import('~/components/LikeVideoHorizontal.vue'))

// 响应式数据
const videoDetail = useState<VideoModel | null>('player.videoDetail', () => null)
const isLoading = ref(true)
const isIntroOpen = ref(false)
const introRef = ref<HTMLElement | null>(null)
const introMaxHeight = ref(0)
const likeVideoList = ref<VideoModel[]>([])
const comingSoonVideoList = ref<VideoModel[]>([])
const playerItemList = ref<VodPlayItem[]>([])
const currentPlayerItem = ref<VodPlayItem | null>(null)
const iframePlayerRef = ref<HTMLIFrameElement | null>(null)
const errorMessages = ref('')
const isHorizontalLayout = ref(false)

// 导航数据
const navigationItems = useNavigation()

// 路由和配置
const route = useRoute()
const config = useRuntimeConfig()

// WindowMessenger实例 - 使用shallowRef避免深度响应式
const windowMessenger = shallowRef<WindowMessenger | null>(null)

// 加载视频详情
const loadVideoDetail = async () => {
    try {
        videoDetail.value = await useFetchApi<VideoModel>("/api/v1/videos/" + route.params.id)
        playerItemList.value = parseVodPlayUrl(videoDetail.value?.videoPlayUrl || '')[0]
        currentPlayerItem.value = playerItemList.value[0]
        isHorizontalLayout.value = GetNavigationExtraParamsVideoTypeName(navigationItems.value, videoDetail.value?.videoType || '').isHorizontalLayout ?? false

        logger.info('视频详情加载完成:', {
            videoId: videoDetail.value?.id,
            playerItemsCount: playerItemList.value?.length,
            currentItem: currentPlayerItem.value?.label
        })
    } catch (error) {
        logger.error('加载视频详情失败:', error)
        errorMessages.value = error instanceof Error ? error.message : '获取视频详情失败'
        throw error
    }
}

// 处理播放器点击事件
const handleClickPlayer = (id: string) => {
    navigateTo({
        path: '/player/' + id,
    })
}

// 获取喜欢的视频列表
const handleGetLikeVideoList = async (video: VideoModel) => {
    try {
        const listRes = await useFetchApi<VideoModel[]>("/api/v1/videos/random", {
            body: {
                videoTag: video.videoTag,
                maxResultCount: 20,
                skipCount: 0,
                sorting: 'Random',
            }
        })
        likeVideoList.value = listRes
    } catch (error) {
        logger.error('获取喜欢视频列表失败:', error)
    }
}

// 获取即将播放视频列表
const handleGetComingSoonVideoList = async (video: VideoModel) => {
    try {
        const listRes = await useFetchApi<VideoModel[]>("/api/v1/videos/random", {
            body: {
                videoType: video.videoType,
                maxResultCount: 5,
                skipCount: 0,
                sorting: 'Random',
            }
        })
        comingSoonVideoList.value = listRes
    } catch (error) {
        logger.error('获取即将播放视频列表失败:', error)
    }
}

// 切换简介显示
const handleToggleIntro = () => {
    isIntroOpen.value = !isIntroOpen.value
    if (introRef.value) {
        if (isIntroOpen.value) {
            introMaxHeight.value = introRef.value.scrollHeight
        } else {
            introMaxHeight.value = 0
        }
    }
}

// 获取下一个播放项
const getNextPlayerItem = (currentUrl: string): VodPlayItem | null => {
    const currentIndex = playerItemList.value.findIndex(item => item.url === currentUrl)
    if (currentIndex === -1 || currentIndex === playerItemList.value.length - 1) {
        return null
    }
    return playerItemList.value[currentIndex + 1]
}

// 切换播放地址
const handleChangePlayerUrl = async (item: VodPlayItem) => {
    currentPlayerItem.value = item
    try {
        if (videoDetail.value?.id !== route.params.id) {
            await loadVideoDetail()
        }
        updateSetMeta()

        // 直接设置iframe src
        if (iframePlayerRef.value && videoDetail.value) {
            const playUrl = videoDetail.value.videoPlayServer + item.url
            logger.info('设置播放地址:', playUrl)
            iframePlayerRef.value.src = playUrl
        }
    } catch (error) {
        logger.error('视频加载错误:', error)
    }
}

// 更新SEO元数据
const updateSetMeta = () => {
    const params = {
        title: videoDetail.value?.videoName || '',
        label: currentPlayerItem.value?.label || '',
        tag: videoDetail.value?.videoTag || '',
        type: videoDetail.value?.videoType || '',
    }
    useSeoMeta({
        title: parseTemplate(config.public.seoMeta.player.title, params),
        keywords: parseTemplate(config.public.seoMeta.player.keywords, params),
        description: parseTemplate(config.public.seoMeta.player.description, params),
        ogTitle: parseTemplate(config.public.seoMeta.player.ogTitle, params),
        ogDescription: parseTemplate(config.public.seoMeta.player.ogDescription, params),
        ogImage: videoDetail.value?.videoPic || '',
    })
}

// 初始化WindowMessenger
const initWindowMessenger = () => {
    // 创建原始对象，使用shallowRef避免深度响应式
    windowMessenger.value = new WindowMessenger({
        targetOrigin: '*',
        onMessage: (data, event) => {
            logger.info('收到消息:', data, event)
        }
    })

    // 监听初始化事件
    windowMessenger.value.onInit(() => {
        logger.info('播放器初始化完成，传递广告数据')
        if (videoDetail.value) {
            // 准备广告数据，确保是纯对象
            const adsData = {
                videoPic: String(videoDetail.value.videoPic || ''),
                ads: JSON.parse(JSON.stringify(getAdvertisementsByPositions([
                    AdvertisementPosition.DuringPlay,
                    AdvertisementPosition.PausePlay,
                    AdvertisementPosition.BeforePlay
                ])))
            }

            logger.log('[播放器广告] 准备传递的广告数据:', adsData)
            windowMessenger.value!.sendInit(adsData)
            logger.log('[播放器广告] 广告数据已通过onInit传递')
        }
    })

    // 监听视频结束事件
    windowMessenger.value.onVideoEnded((data) => {
        logger.info('视频播放结束，准备联播:', data)
        const { manifestUri } = data
        const nextItem = getNextPlayerItem(manifestUri)
        if (nextItem) {
            handleChangePlayerUrl(nextItem)
        }
    })

    logger.info('WindowMessenger初始化完成')
}

// 设置播放器和消息通信
const setupPlayerAndMessenger = () => {
    nextTick(() => {
        if (iframePlayerRef.value && currentPlayerItem.value && windowMessenger.value) {
            // 获取原始的contentWindow，避免Vue响应式包装
            const contentWindow = iframePlayerRef.value.contentWindow
            logger.info('获取到contentWindow:', !!contentWindow)

            if (contentWindow) {
                windowMessenger.value.setWindow(contentWindow)
                logger.info('设置消息通信目标窗口完成')

                // 设置播放地址
                handleChangePlayerUrl(currentPlayerItem.value)
            }
        }
    })
}

// 页面加载时初始化
await callOnce('player.loadVideoDetail', async () => {
    await loadVideoDetail()
})

// 组件挂载
onMounted(async () => {
    try {
        // 初始化WindowMessenger
        initWindowMessenger()

        // 加载相关视频数据
        if (videoDetail.value) {
            await Promise.all([
                handleGetLikeVideoList(videoDetail.value),
                handleGetComingSoonVideoList(videoDetail.value)
            ])
        }

        // 设置播放器
        setupPlayerAndMessenger()

        isLoading.value = false
    } catch (error) {
        logger.error('页面初始化失败:', error)
        errorMessages.value = error instanceof Error ? error.message : '页面初始化失败'
    }
})

// 组件卸载
onUnmounted(() => {
    if (windowMessenger.value) {
        windowMessenger.value.destroy()
        windowMessenger.value = null
    }
})

// 更新SEO元数据
updateSetMeta()
</script>

<style scoped lang="scss">
@import '@/static/css/view.css';
</style>
