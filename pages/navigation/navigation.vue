<template>
  <view class="container">
    <!-- 加载中提示 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载...</text>
    </view>

    <!-- 错误提示 -->
    <view class="error-container" v-else-if="error">
      <text class="error-text">加载失败</text>
      <view class="error-retry" @click="loadNavigations">点击重试</view>
    </view>

    <!-- 分类内容 -->
    <view class="navigation" v-else>
      <!-- 左侧分类列表 -->
      <view class="sidebar">
        <view
            v-for="(category, index) in mainCategories"
            :key="index"
            class="item"
            :class="{ active: activeCategoryIndex === index }"
            @click="changeSidebarCategory(index)"
        >
          {{ category }}
        </view>
      </view>

      <!-- 右侧子分类网格 -->
      <view class="content">
        <!-- 子分类加载中 -->
        <view class="subcategory-loading" v-if="currentSubCategories.length === 0 && !error && subCategoryLoading">
          <view class="loading-spinner"></view>
          <text class="loading-text">正在加载...</text>
        </view>

        <!-- 无子分类提示 -->
        <view class="subcategory-empty" v-else-if="currentSubCategories.length === 0 && !error && !subCategoryLoading">
          <text class="empty-text">暂无内容</text>
        </view>

        <!-- 子分类列表 -->
        <view
            v-for="(item, index) in currentSubCategories"
            :key="index"
            class="item"
            @click="navigateToList(item)"
        >
          {{ item.name }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCachedNavigationList } from '@/api/navigationCache';
import { getVideoTagList, TagDisplayType } from '@/api/videoTag';

export default {
  data() {
    return {
      loading: false,
      error: null,
      navigations: [],           // 原始导航数据
      mainCategories: [],        // 主分类列表（左侧边栏）
      subCategoriesMap: {},      // 子分类映射（右侧内容）
      activeCategoryIndex: 0,    // 当前选中的主分类索引
      currentSubCategories: [],  // 当前显示的子分类
      subCategoryLoading: false  // 子分类加载状态
    };
  },

  // 页面加载时执行
  onLoad() {
    this.loadNavigations();
  },

  methods: {
    // 加载导航数据
    loadNavigations() {
      this.loading = true;
      this.error = null;

      // 使用缓存服务获取导航数据
      getCachedNavigationList()
        .then(async response => {
          if (response && Array.isArray(response)) {
            // 保存原始导航数据
            this.navigations = response;

            // 处理导航数据，提取主分类和子分类
            await this.processNavigationData(response);
          } else {
            throw new Error('获取导航数据失败');
          }
        })
        .catch(error => {
          console.error('加载导航数据失败:', error);
          this.error = error.message || '加载导航数据失败';
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理导航数据
    async processNavigationData(navigations) {
      // 按categotyName分组
      const groupedNavigations = {};

      // 过滤移动端显示的导航项
      const mobileNavigations = navigations.filter(nav => nav.isH5Display);

      // 提取主分类（一级分类）
      mobileNavigations.forEach(nav => {
        // 使用categotyName作为主分类名称
        const mainCategory = nav.categotyName;

        if (mainCategory) {
          if (!groupedNavigations[mainCategory]) {
            groupedNavigations[mainCategory] = [];
          }

          // 将导航项添加到对应的主分类中
          groupedNavigations[mainCategory].push(nav);
        }
      });

      // 转换为数组格式
      this.mainCategories = Object.keys(groupedNavigations);

      // 创建子分类映射
      this.subCategoriesMap = {};

      try {
        // 设置子分类加载状态
        this.subCategoryLoading = true;

        // 一次性获取所有分类的标签数据（不指定videoTypeId，获取所有标签）
        const allTags = await getVideoTagList([TagDisplayType.Category], null);

        if (allTags && Array.isArray(allTags)) {
          // 创建分类ID到分类名称的映射
          const categoryIdToName = {};
          const categoryNameToIndex = {};

          for (let i = 0; i < this.mainCategories.length; i++) {
            const category = this.mainCategories[i];
            const categoryItems = groupedNavigations[category];
            categoryNameToIndex[category] = i;

            if (categoryItems && categoryItems.length > 0) {
              const categoryId = categoryItems[0].categotyId;
              categoryIdToName[categoryId] = category;
            }
          }

          // 为每个主分类分组标签数据
          for (let i = 0; i < this.mainCategories.length; i++) {
            const category = this.mainCategories[i];
            const categoryItems = groupedNavigations[category];

            if (categoryItems && categoryItems.length > 0) {
              const categoryId = categoryItems[0].categotyId;

              // 从所有标签中筛选属于当前分类的标签
              // 根据标签的types字段或者通过分类ID匹配
              const subCategories = allTags
                .filter(tag => {
                  // 如果标签有types字段，检查是否包含当前分类
                  if (tag.types && Array.isArray(tag.types)) {
                    return tag.types.includes(category) || tag.types.includes(categoryId);
                  }
                  // 如果没有types字段，暂时将所有标签分配给所有分类
                  // 这是一个fallback方案，实际使用时可能需要调整
                  return true;
                })
                .map(tag => ({
                  id: tag.id,
                  name: tag.tagName,
                  categotyId: categoryId,
                  categotyName: category
                }));

              // 存储子分类数据
              this.subCategoriesMap[i] = subCategories;
            } else {
              this.subCategoriesMap[i] = [];
            }
          }
        } else {
          console.warn('获取标签数据返回空数据');
          // 初始化所有分类为空数组
          for (let i = 0; i < this.mainCategories.length; i++) {
            this.subCategoriesMap[i] = [];
          }
        }

        console.log('一次性加载的标签数据:', allTags);
        console.log('分组后的子分类映射:', this.subCategoriesMap);
      } catch (error) {
        console.error('加载标签数据失败:', error);
        // 如果一次性加载失败，回退到原来的逐个加载方式
        console.log('回退到逐个加载标签数据');
        await this.loadTagsIndividually(groupedNavigations);
      } finally {
        // 重置子分类加载状态
        this.subCategoryLoading = false;
      }

      // 设置默认显示的子分类
      if (this.mainCategories.length > 0) {
        this.currentSubCategories = this.subCategoriesMap[0] || [];
      }
    },

    // 回退方案：逐个加载标签数据（原来的方式）
    async loadTagsIndividually(groupedNavigations) {
      for (let i = 0; i < this.mainCategories.length; i++) {
        const category = this.mainCategories[i];
        const categoryItems = groupedNavigations[category];

        if (categoryItems && categoryItems.length > 0) {
          const categoryId = categoryItems[0].categotyId;

          try {
            // 加载标签数据作为子分类
            const tags = await getVideoTagList([TagDisplayType.Category], categoryId);

            if (tags && Array.isArray(tags)) {
              // 将标签数据转换为子分类格式
              const subCategories = tags.map(tag => ({
                id: tag.id,
                name: tag.tagName,
                categotyId: categoryId,
                categotyName: category
              }));

              // 存储子分类数据
              this.subCategoriesMap[i] = subCategories;
            } else {
              this.subCategoriesMap[i] = [];
            }
          } catch (error) {
            console.error(`加载${category}的子分类失败:`, error);
            this.subCategoriesMap[i] = [];
          }
        } else {
          this.subCategoriesMap[i] = [];
        }
      }
    },

    // 切换主分类
    changeSidebarCategory(index) {
      this.activeCategoryIndex = index;

      // 更新当前显示的子分类
      if (this.subCategoriesMap[index]) {
        this.currentSubCategories = this.subCategoriesMap[index];
      } else {
        // 如果还没有加载该分类的子分类，显示空数组
        this.currentSubCategories = [];

        // 如果子分类映射中没有该索引，可能是还没有加载完成
        // 这里不需要额外的加载逻辑，因为processNavigationData会加载所有分类的子分类
      }
    },

    // 导航到列表页
    navigateToList(item) {
      if (!item) return;

      // 构建URL参数
      let params = {};

      // 添加视频类型
      if (item.categotyName) {
        params.videoType = item.categotyName;
      }

      // 添加视频类型ID
      if (item.categotyId) {
        params.videoTypeId = item.categotyId;
      }

      // 添加标签
      if (item.name && item.name !== item.categotyName) {
        params.videoTag = item.name;
      }

      // 添加页面标题
      params.title = item.name || item.categotyName;

      // 添加排序方式（默认最新上传）
      params.sorting = 'VideoAddTime desc';

      // 构建URL查询字符串
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      // 导航到列表页
      uni.navigateTo({
        url: `/pages/list/list?${queryString}`
      });
    }
  }
};
</script>


<style scoped lang="scss">
// 加载中状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 88rpx);
  width: 100%;
  background-color: #17181a;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #333;
    border-top: 4rpx solid #fe748e;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #bbb;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 错误提示样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 88rpx);
  width: 100%;
  background-color: #17181a;

  .error-text {
    font-size: 24rpx;
    color: #bbb;
    margin-bottom: 20rpx;
  }

  .error-retry {
    padding: 10rpx 30rpx;
    background-color: #fe748e;
    color: #fff;
    font-size: 24rpx;
    border-radius: 30rpx;
    cursor: pointer;
  }
}

// 导航内容样式
.navigation {
  display: flex;

  .sidebar {
    width: 30vw;
    flex: none;
    font-size: 28rpx;

    .item {
      height: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .active {
      background: #17181a;
      color: #fe748e;
      font-size: 36rpx;
      font-weight: 600;
    }
  }

  .content::-webkit-scrollbar {
    display: none;
  }

  .content {
    background: #17181a;
    display: flex;
    flex-wrap: wrap;
    height: calc(100vh - 88rpx);
    overflow-y: scroll;
    align-content: flex-start;
    width: 100%;

    // 子分类加载中样式
    .subcategory-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 300rpx;

      .loading-spinner {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid #333;
        border-top: 4rpx solid #fe748e;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        margin-top: 20rpx;
        font-size: 24rpx;
        color: #bbb;
      }
    }

    // 无子分类提示样式
    .subcategory-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 300rpx;

      .empty-text {
        font-size: 28rpx;
        color: #bbb;
      }
    }

    .item {
      flex: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100rpx;
      width: 50%;
    }
  }
}
</style>
