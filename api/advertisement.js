/**
 * 广告相关API
 */
import request from '../utils/request';

// 广告位置枚举
export const AdvertisementPosition = {
  HomeTop: 0,         // 首页顶部
  CategoryTopRight: 1, // 分类页顶部右侧
  PlayerTopRight: 2,   // 播放页顶部右侧
  PlayerBottom: 3,     // 播放器底部
  SearchTopRight: 4,   // 搜索页顶部右侧
  BeforePlay: 5,       // 播放前
  DuringPlay: 6,       // 播放中
  PausePlay: 7,        // 播放暂停
  SplashScreen: 8,     // 开屏广告
};

/**
 * 获取广告列表
 * @returns {Promise<Array>} 广告列表
 */
export function getAdvertisements() {
  return request.post('/api/v1/advertisements');
}

/**
 * 按位置过滤广告
 * @param {Array} advertisements - 广告列表
 * @param {number} position - 广告位置
 * @returns {Array} 过滤后的广告列表
 */
export function filterAdvertisementsByPosition(advertisements, position) {
  if (!advertisements || !Array.isArray(advertisements)) {
    return [];
  }

  return advertisements.filter(ad => ad.position === position)
    .sort((a, b) => a.sort - b.sort);
}

/**
 * 根据多个位置获取广告
 * @param {Array} advertisements - 广告列表
 * @param {Array} positions - 广告位置数组
 * @returns {Array} 过滤后的广告列表
 */
export function getAdvertisementsByPositions(advertisements, positions) {
  if (!advertisements || !Array.isArray(advertisements)) {
    return [];
  }

  if (!positions || !Array.isArray(positions)) {
    return [];
  }

  return advertisements
    .filter(ad => positions.includes(ad.position))
    .sort((a, b) => a.sort - b.sort);
}

/**
 * 获取指定位置的第一个广告
 * @param {Array} advertisements - 广告列表
 * @param {number} position - 广告位置
 * @returns {Object|null} 广告对象或null
 */
export function getFirstAdvertisementByPosition(advertisements, position) {
  const ads = filterAdvertisementsByPosition(advertisements, position);
  return ads.length > 0 ? ads[0] : null;
}
